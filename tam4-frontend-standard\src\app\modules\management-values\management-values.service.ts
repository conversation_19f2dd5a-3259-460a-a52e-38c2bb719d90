import { Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { DialogService, DynamicDialogConfig } from "primeng/dynamicdialog";
import { CommonResponseWrapper, ResponseOutcomeType } from 'src/app/models';
import { defaultModalStyleBig } from 'src/app/utils/common.constants';
import { HttpClientUtils } from '../../utils';
import { ChangesHistoryDetailsComponent } from './components/changes-history-details.component';
import { ManagementValuesDao } from './management-values.dao';
import {
  ChangesHistoryItem,
  ConstraintsAttributeUpdate, DomainValueAttributeUpdate,
  DomainValueChanges,
  DomainValueChangesRequestItem,
  PageOptionsRequest, SelectedTreeDomainAttribute,
  TreeAttributesRequest
} from './models/management-values.types';
import { MANAGEMENT_VALUES_ACTIONS } from "./store/management-values.actions";
import { ManagementValuesState } from "./store/management-values.state";
import { MessageService } from 'primeng/api';

@Injectable({
  providedIn: 'root'
})
export class ManagementValuesService {

  constructor(
    private store: Store<ManagementValuesState>,
    protected translate: TranslateService,
    private dialogService: DialogService,
    private messageService: MessageService,
    private dao: ManagementValuesDao
  ) {}

  // TODO: refactor this method to use a more structured type, enum in a personlized pipe
  getSeverity(value: string): "success" | "secondary" | "info" | "warning" | "danger" | "contrast" {
    if (value === 'ADD' || value === 'FEEDBACK_RECEIVED') {
      return 'success';
    } else if (value === 'UPDATE' || value === 'SENT') {
      return 'info';
    } else if (value === 'PENDING') {
      return 'warning';
    } else if (value === 'DELETE') {
      return 'danger';
    } else {
      return 'secondary';
    }
  }

  getManagementValuesTreeAttributesRequest(language: string): TreeAttributesRequest {
    return {
      language
    };
  }

  getPageOptionsRequest(page: number, itemsPerPage: number, attributeSelected?: string, intervalDateSelected?: string): PageOptionsRequest {
    return {
      page,
      itemsPerPage,
      attributeSelected,
      intervalDateSelected
    };
  }

  action_doInit() {
    this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.INIT());
  }

  action_doTreeAttributes() {
    this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.TREE_ATTRIBUTES());
  }

  action_doTreeAttributesSuccess() {
    return (resp: DomainValueChanges): void => {
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.TREE_ATTRIBUTES_SUCCESS(resp));
    };
  }

  action_doTreeAttributesFailure() {
    return (err): void => {
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.TREE_ATTRIBUTES_FAILURE(HttpClientUtils.getErrorWrapper()));
    };
  }

  action_doTreeAttributesSelect(attributeNode: SelectedTreeDomainAttribute) {
    this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.TREE_ATTRIBUTES_SELECT(attributeNode));
  }

  action_doGetConstraint(attribute) {
    this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.GET_CONSTRAINT(attribute));
  }

  action_doUpdateConstraint(constraintData: ConstraintsAttributeUpdate) {
    this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.UPDATE_CONSTRAINT(constraintData));
  }

  action_doUpdateConstraintSuccess() {
    return (resp: CommonResponseWrapper<any>): void => {
      if (resp.outcome === ResponseOutcomeType.ERROR) {
        this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.UPDATE_CONSTRAINT_FAILURE(resp));
        return;
      }
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.UPDATE_CONSTRAINT_SUCCESS(resp));
    };
  }

  action_doGetChangeHistory(page: number, itemsPerPage: number, attributeSelected?: string, intervalDateSelected?: string) {
    this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.GET_CHANGE_HISTORY({page, itemsPerPage, attributeSelected, intervalDateSelected}));
  }

  action_doUpdateDomainValues(domainValueAttributeUpdate: DomainValueAttributeUpdate) {
    this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.UPDATE_DOMAIN_VALUES(domainValueAttributeUpdate));
  }

  action_doValidateDeleteAttributeValue(attributeId: string, valueId: string, type: string, callbackOk: (resp: boolean) => void, callbackKo: (err: any) => void) {
    this.dao.validateDeleteAttributeValue(
      attributeId,
      valueId,
      type,
      (resp: boolean) => {
        callbackOk(resp);
      },
      (err: any) => {
        callbackKo(err);
      }
    );
  }

  action_doValidateChanges(domainValueChangesRequest: DomainValueChangesRequestItem[], callbackOk: (resp: boolean) => void, callbackKo: (err: any) => void) {
    this.dao.validateChanges(
      domainValueChangesRequest,
      (resp: boolean) => {
        callbackOk(resp);
      },
      (err: any) => {
        callbackKo(err);
      }
    );
  }

  action_doUpdateTreeNodeExpandedState(nodeKey: string, expanded: boolean) {
    this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.UPDATE_TREE_NODE_EXPANDED_STATE({ nodeKey, expanded }));
  }

  action_doGetChangeHistorySuccess() {
    return (resp: any[]): void => {
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.GET_CHANGE_HISTORY_SUCCESS(resp));
    };
  }

  action_doGetChangeHistoryFailure() {
    return (err): void => {
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.GET_CHANGE_HISTORY_FAILURE(HttpClientUtils.getErrorWrapper()));
    };
  }

  openHistoryItemDetails(item: ChangesHistoryItem) {
    const modalCfg: DynamicDialogConfig = {
      ...defaultModalStyleBig,
      showHeader: true,
      closable: false,
      height: 'auto',
      data: item
    };
    this.dialogService.open(ChangesHistoryDetailsComponent, modalCfg);
  }

  action_doGetConstraintSuccess() {
    return (resp: CommonResponseWrapper<any>): void => {
      if (resp.outcome === ResponseOutcomeType.ERROR) {
        this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.GET_CONSTRAINT_FAILURE(resp));
        return;
      }
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.GET_CONSTRAINT_SUCCESS(resp));
    };
  }

  action_doGetConstraintFailure() {
    return (err): void => {
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.GET_CONSTRAINT_FAILURE(HttpClientUtils.getErrorWrapper()));
    };
  }

  action_doSaveChanges(domainValueChangesRequest: DomainValueChangesRequestItem[]) {
    this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.SAVE_CHANGES(domainValueChangesRequest));
  }

  action_doSaveChangesSuccess_() {
    return (resp: any): void => {
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.SAVE_CHANGES_SUCCESS(resp));
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.TREE_ATTRIBUTES());
      this.messageService.add({
        severity: 'success',
        summary: this.translate.instant('domainValuesManagement.messages.success'),
        detail: this.translate.instant('domainValuesManagement.messages.saveSuccess')
      });
    };
  }

  action_doSaveChangesSuccess() {
    return (resp: any): void => {
      console.log(resp);
      if (resp.outcome === ResponseOutcomeType.ERROR) {
        this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.SAVE_CHANGES_FAILURE(resp));
        return;
      }
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.SAVE_CHANGES_SUCCESS(resp));
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.TREE_ATTRIBUTES());
      this.messageService.add({
        severity: 'success',
        summary: this.translate.instant('domainValuesManagement.messages.success'),
        detail: this.translate.instant('domainValuesManagement.messages.saveSuccess')
      });
    };
  }

  action_doSaveChangesFailure(){
    return (err): void => {
      this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.SAVE_CHANGES_FAILURE(HttpClientUtils.getErrorWrapper()));
    };
  }

  action_doSaveChangesClean() {
    this.store.dispatch(MANAGEMENT_VALUES_ACTIONS.SAVE_CHANGES_CLEAN());
  }
}
