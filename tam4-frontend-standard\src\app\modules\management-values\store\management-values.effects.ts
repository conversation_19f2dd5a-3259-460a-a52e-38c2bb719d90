import { Injectable } from '@angular/core';
import { Actions, createEffect, ofType } from '@ngrx/effects';
import { Store } from '@ngrx/store';
import {catchError, exhaustMap, tap, withLatestFrom} from 'rxjs/operators';
import { ReduxUtils } from "src/app/utils";
import { getCurrentLanguage } from '../../layout/store/profile/profile.state';
import { ManagementValuesDao } from "../management-values.dao";
import { ManagementValuesService } from "../management-values.service";
import { ConstraintsAttribute } from '../models/management-values.types';
import { MANAGEMENT_VALUES_ACTION_NAMES, ManagementValuesAction_Get_Constraint, ManagementValuesAction_Save_Changes, ManagementValuesAction_Tree_Attributes, ManagementValuesActionTypes } from "./management-values.actions";
import { ManagementValuesSelectors } from './management-values.selectors';
import { ManagementValuesState } from "./management-values.state";
import { EMPTY } from 'rxjs';


@Injectable({providedIn: 'root'})
export class ManagementValuesEffects {

  constructor(private actions$: Actions<ManagementValuesActionTypes>,
              private service: ManagementValuesService,
              private dao: ManagementValuesDao,
              private store: Store<ManagementValuesState>
  ) { }

  onSearchTreeAttributes = createEffect(() => this.actions$.pipe(
    ofType(MANAGEMENT_VALUES_ACTION_NAMES.TREE_ATTRIBUTES),
    withLatestFrom(this.store.select(getCurrentLanguage)),
    tap((inputs: [ManagementValuesAction_Tree_Attributes, string]) => {
      return this.dao.treeAttributes(
        this.service.getManagementValuesTreeAttributesRequest(inputs[1]),
        this.service.action_doTreeAttributesSuccess(),
        this.service.action_doTreeAttributesFailure());
    })
  ), ReduxUtils.noDispatch());

  onGetChangeHistory = createEffect(() => this.actions$.pipe(
    ofType(MANAGEMENT_VALUES_ACTION_NAMES.GET_CHANGE_HISTORY),
    tap(a$ => {
      return this.dao.changeHistory(
        this.service.getPageOptionsRequest(a$.payload.page, a$.payload.itemsPerPage, a$.payload.attributeSelected, a$.payload.intervalDateSelected),
        this.service.action_doGetChangeHistorySuccess(),
        this.service.action_doGetChangeHistoryFailure());
    })
  ), ReduxUtils.noDispatch());

    onGetConstraint = createEffect(() =>
            this.actions$.pipe(
                ofType(MANAGEMENT_VALUES_ACTION_NAMES.GET_CONSTRAINT),
                withLatestFrom(
                    this.store.select(getCurrentLanguage),
                    this.store.select(ManagementValuesSelectors.getConstraints)
                ),
                exhaustMap(([action, language, constraints]) =>
                    this.handleConstraintRequest(action.payload, language, constraints).pipe(
                        catchError(() => EMPTY)
                    )
                )
            ),
        { dispatch: false }
    );

  onSaveChanges = createEffect(() => this.actions$.pipe(
    ofType(MANAGEMENT_VALUES_ACTION_NAMES.SAVE_CHANGES),
    tap((action: ManagementValuesAction_Save_Changes) => {
      this.dao.submitChanges(
        action.payload, // changes[]
        this.service.action_doSaveChangesSuccess(),
        this.service.action_doSaveChangesFailure()
      );
    })
  ), ReduxUtils.noDispatch());

    private handleConstraintRequest(
        attribute: { key: string },
        language: string,
        constraints: ConstraintsAttribute[]
    ) {
        const existingConstraint = constraints.find(c => c.attributeId === attribute.key);
        if (existingConstraint) {
            this.dispatchSuccessResponse(attribute.key, existingConstraint);
            return EMPTY;
        }
        return this.fetchNewConstraint(attribute.key, language);
    }

    private dispatchSuccessResponse(
        attributeId: string,
        constraint: ConstraintsAttribute
    ) {
        this.store.dispatch({
            type: MANAGEMENT_VALUES_ACTION_NAMES.GET_CONSTRAINT_SUCCESS,
            payload: {
                outcome: 'SUCCESS',
                attributeId,
                constraints: constraint
            }
        });
    }

    private fetchNewConstraint(attributeId: string, language: string) {
        this.dao.getConstraint(
            attributeId,
            language,
            this.service.action_doGetConstraintSuccess(),
            this.service.action_doGetConstraintFailure()
        );
        return EMPTY;
    }

}
